'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { ExternalLink, Search, Globe, Star, Clock } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

interface BookmarkItem {
  title: string
  url: string
  description?: string
  isVercelSearch?: boolean
  isPlatformSearch?: boolean
  searchDomain?: string
}

interface BookmarkCategory {
  name: string
  items: BookmarkItem[]
}

// 通用平台查词组件
function PlatformSearchCard({ item }: { item: BookmarkItem }) {
  const [timeRange, setTimeRange] = useState('d')

  const timeOptions = [
    { value: 'h', label: '过去1小时' },
    { value: 'd', label: '过去1天' },
    { value: 'w', label: '过去1周' },
    { value: 'm', label: '过去1个月' }
  ]

  const getPlatformUrl = (range: string) => {
    return `https://www.google.com/search?q=site:${item.searchDomain}&tbs=qdr:${range}`
  }

  const getPlatformName = () => {
    if (item.searchDomain === 'vercel.app') return 'Vercel'
    if (item.searchDomain === 'netlify.app') return 'Netlify'
    if (item.searchDomain === 'onrender.com') return 'Render'
    if (item.searchDomain === 'surge.sh') return 'Surge'
    if (item.searchDomain === 'web.app') return 'Firebase'
    if (item.searchDomain === 'firebaseapp.com') return 'Firebase'
    if (item.searchDomain === 'github.io') return 'GitHub Pages'
    if (item.searchDomain === 'glitch.me') return 'Glitch'
    if (item.searchDomain === 'railway.app') return 'Railway'
    if (item.searchDomain === 'pages.dev') return 'Cloudflare Pages'
    return '平台'
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-semibold text-foreground group-hover:text-primary transition-colors flex items-center">
          <Globe className="h-4 w-4 mr-2 text-muted-foreground group-hover:text-primary" />
          <span className="truncate">{item.title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-3 h-10 overflow-hidden">
          {item.description || '优质网站资源'}
        </p>

        {/* 时间范围选择器 */}
        <div className="mb-3 p-3 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-lg border border-blue-100">
          <div className="flex items-center mb-2">
            <Clock className="h-4 w-4 mr-2 text-blue-600" />
            <span className="text-sm font-semibold text-blue-800">时间范围</span>
          </div>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-full h-9 text-sm border-2 border-blue-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200/50 transition-all duration-200 bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 shadow-sm hover:shadow-md font-medium">
              <SelectValue className="text-blue-700" />
            </SelectTrigger>
            <SelectContent className="border-2 border-blue-200 shadow-xl rounded-lg bg-white/95 backdrop-blur-md min-w-[160px] z-50">
              {timeOptions.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="text-sm py-2.5 pl-3 pr-3 cursor-pointer hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:text-blue-700 focus:bg-gradient-to-r focus:from-blue-100 focus:to-purple-100 focus:text-blue-800 data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-500 data-[state=checked]:to-purple-600 data-[state=checked]:text-white transition-all duration-200 rounded-md mx-1 my-0.5 font-medium hover:scale-[1.02] active:scale-[0.98] [&>span:first-child]:hidden"
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          asChild
          size="sm"
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200"
        >
          <a
            href={getPlatformUrl(timeRange)}
            target="_blank"
            rel="nofollow noopener noreferrer"
            className="flex items-center justify-center"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            搜索{getPlatformName()}站点
          </a>
        </Button>
      </CardContent>
    </Card>
  )
}

// Vercel查词组件（保持向后兼容）
function VercelSearchCard({ item }: { item: BookmarkItem }) {
  const [timeRange, setTimeRange] = useState('d')

  const timeOptions = [
    { value: 'h', label: '过去1小时' },
    { value: 'd', label: '过去1天' },
    { value: 'w', label: '过去1周' },
    { value: 'm', label: '过去1个月' }
  ]

  const getVercelUrl = (range: string) => {
    return `https://www.google.com/search?q=site:vercel.app&tbs=qdr:${range}`
  }

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-semibold text-foreground group-hover:text-primary transition-colors flex items-center">
          <Globe className="h-4 w-4 mr-2 text-muted-foreground group-hover:text-primary" />
          <span className="truncate">{item.title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-3 h-10 overflow-hidden">
          {item.description || '优质网站资源'}
        </p>

        {/* 时间范围选择器 */}
        <div className="mb-3 p-3 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-lg border border-blue-100">
          <div className="flex items-center mb-2">
            <Clock className="h-4 w-4 mr-2 text-blue-600" />
            <span className="text-sm font-semibold text-blue-800">时间范围</span>
          </div>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-full h-9 text-sm border-2 border-blue-200 hover:border-blue-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200/50 transition-all duration-200 bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 shadow-sm hover:shadow-md font-medium">
              <SelectValue className="text-blue-700" />
            </SelectTrigger>
            <SelectContent className="border-2 border-blue-200 shadow-xl rounded-lg bg-white/95 backdrop-blur-md min-w-[160px] z-50">
              {timeOptions.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="text-sm py-2.5 pl-3 pr-3 cursor-pointer hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:text-blue-700 focus:bg-gradient-to-r focus:from-blue-100 focus:to-purple-100 focus:text-blue-800 data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-500 data-[state=checked]:to-purple-600 data-[state=checked]:text-white transition-all duration-200 rounded-md mx-1 my-0.5 font-medium hover:scale-[1.02] active:scale-[0.98] [&>span:first-child]:hidden"
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          asChild
          size="sm"
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200"
        >
          <a
            href={getVercelUrl(timeRange)}
            target="_blank"
            rel="nofollow noopener noreferrer"
            className="flex items-center justify-center"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            搜索Vercel站点
          </a>
        </Button>
      </CardContent>
    </Card>
  )
}

export default function BookmarksPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')



  // 书签数据
  const bookmarkCategories: BookmarkCategory[] = [
    {
      name: '站内工具',
      items: [
        { title: '新词过滤', url: '/filter', description: '智能过滤和筛选新关键词' },
        { title: '链接过滤', url: '/backlink-filter', description: '反向链接分析和过滤工具' },
        { title: '模拟抓取', url: '/spider', description: '网页内容抓取和分析工具' },
        { title: '词根导航', url: '/roots', description: 'AI工具站和小游戏词根关键词导航' },
        { title: '需求导航', url: '/needs', description: '全年龄段用户需求导向关键词导航' },
        { title: 'AdSense计算器', url: '/adsense-calculator', description: 'Google AdSense收入计算器，支持CPC和CPM双重收益模式' },
        { title: '工具关键词大全', url: '/tool-keywords', description: '130+工具站关键词完整清单，涵盖10大分类与新兴趋势' },
        { title: 'KGR计算器', url: '/kgr-calculator', description: 'KGR关键词黄金比例计算器，快速识别易排名关键词' },
        { title: '营销ROI计算器', url: '/marketing-roi-calculator', description: '营销投资回报率综合分析工具，支持CAC、LTV、ROAS等关键指标计算' },
        { title: 'App Store分析工具', url: '/app-analyzer', description: 'App Store链接转SensorTower分析工具，支持单个和批量处理' },
        { title: 'Sitemap检测工具', url: '/sitemap-checker', description: '批量检测网站sitemap.txt文件，支持一键下载，最多10个域名' },
        { title: 'CatchROI计算器', url: '/catchroi-calculator', description: 'CatchROI = Volume × CPC ÷ KD，通过搜索量、点击单价和优化难度计算关键词价值' },
        { title: '落地页SEO指南', url: '/landing-page-seo-guide', description: '标题优化、关键词布局、H标签架构和技术SEO完整优化策略' },
        { title: '谷歌趋势查询工具', url: '/google-trends-generator', description: '每行输入一个关键词，自动分组生成谷歌趋势查询链接，每组5个关键词，一键批量打开' },
        { title: 'Google搜索结果分析工具', url: '/google-search-analyzer', description: '分析Google搜索结果前10名页面的SEO策略，包括标题、域名、摘要、Meta标签等，支持批量处理和MD导出' },
        { title: 'PH中文', url: 'https://news.catchideas.com/user.php?id=0xPH', description: 'Product Hunt产品发现中文版' },
        { title: 'HN中文', url: 'https://news.catchideas.com/user.php?id=0xHN', description: 'Hacker News技术资讯中文版' }
      ]
    },
    {
      name: '谷歌',
      items: [
        {
          title: 'AdSense',
          url: 'https://www.google.com/adsense/',
          description: '谷歌广告联盟平台，网站变现首选'
        },
        {
          title: 'Gmail',
          url: 'https://mail.google.com/mail/u/0/#inbox',
          description: '谷歌邮箱服务，全球通用邮件系统'
        },
        {
          title: 'Translate',
          url: 'https://translate.google.com/',
          description: '谷歌翻译工具，支持多种语言互译'
        }
      ]
    },
    {
      name: 'IDC',
      items: [
        {
          title: 'Cloudflare',
          url: 'https://dash.cloudflare.com/',
          description: 'CDN和网络安全服务平台'
        },
        {
          title: '阿里云',
          url: 'https://www.aliyun.com/',
          description: '阿里巴巴云计算服务平台'
        },
        {
          title: 'Vercel',
          url: 'https://vercel.com/',
          description: '前端项目部署和托管平台'
        },
        {
          title: 'Supabase',
          url: 'https://supabase.com/',
          description: '开源Firebase替代方案，后端即服务平台'
        }
      ]
    },
    {
      name: 'AI',
      items: [
        {
          title: 'ChatGPT',
          url: 'https://chatgpt.com/',
          description: 'OpenAI智能对话助手'
        },
        {
          title: 'Claude',
          url: 'https://claude.ai/new',
          description: 'Anthropic AI助手，擅长分析推理'
        },
        {
          title: 'Minimaxi',
          url: 'https://chat.minimaxi.com/',
          description: 'MiniMax海螺AI，多模态大模型，支持文本对话和图像理解'
        },
        {
          title: 'Gemini',
          url: 'https://gemini.google.com/app',
          description: 'Google最新多模态AI助手，强大的推理和创作能力'
        },
        {
          title: 'Kimi',
          url: 'https://www.kimi.com/',
          description: 'Moonshot AI智能助手，支持超长文本处理和K2模型'
        },
        {
          title: 'Manus',
          url: 'https://manus.im/guest',
          description: 'AI写作和内容创作助手'
        },
        {
          title: 'Grok',
          url: 'https://grok.com/',
          description: 'xAI开发的AI助手，具有实时信息获取和幽默对话能力'
        }
      ]
    },
    {
      name: '工具',
      items: [
        { title: 'Aihubmix', url: 'https://aihubmix.com?aff=2BlX', description: '模型批发平台，支持支付宝支付' },
        { title: 'Deepl', url: 'https://www.deepl.com/zh/translator', description: '专业AI翻译工具' },
        { title: 'Cursor', url: 'https://www.cursor.com/cn', description: 'AI代码编辑器，智能编程助手' },
        { title: 'Semrush', url: 'https://dash.seogroup.club/?inv=31e9c3#/register', description: 'SEO分析工具代理服务' },
        { title: 'Analytics', url: 'https://analytics.google.com/analytics/web/#/', description: '谷歌网站流量分析工具' },
        { title: 'Notion', url: 'https://www.notion.so/', description: '全能笔记和协作工作空间' },
        { title: 'KiroAI', url: 'https://kiroai.net/', description: '亚马逊开发的IDE AI编程工具，智能代码助手和开发环境' }
      ]
    },
    {
      name: '支付',
      items: [
        { title: 'WorldFirst', url: 'https://portal.worldfirst.com.cn/', description: '万里汇跨境支付解决方案' },
        { title: 'PayPal', url: 'https://www.paypal.com/', description: '全球领先的在线支付平台' },
        { title: 'Payoneer', url: 'https://www.payoneer.com/zh-hans/', description: '派安盈跨境收款服务' },
        { title: 'Wise', url: 'https://wise.com/', description: '国际汇款和多币种账户' },
        { title: 'Lemon Squeezy', url: 'https://www.lemonsqueezy.com/', description: '数字产品销售和订阅平台' },
        { title: 'Ko-fi', url: 'https://ko-fi.com/', description: '创作者打赏和支持平台' },
        { title: 'Buy Me a Coffee', url: 'https://buymeacoffee.com/', description: '创作者支持和打赏平台' },
        { title: 'Paddle', url: 'https://www.paddle.com/', description: '软件产品销售和订阅管理' },
        { title: 'Gumroad', url: 'https://gumroad.com/', description: '数字内容销售平台' },
        { title: '倍易付 vvacard', url: 'https://www.vvacard.com/', description: '虚拟信用卡服务平台' }
      ]
    },
    {
      name: '广告联盟',
      items: [
        { title: 'Amazon.com Associates Central', url: 'https://affiliate-program.amazon.com/', description: '亚马逊联盟营销计划' },
        { title: 'CJ Affiliate', url: 'https://members.cj.com/', description: 'CJ联盟营销平台' },
        { title: 'JVZoo', url: 'https://www.jvzoo.com/', description: '数字产品联盟营销平台' }
      ]
    },
    {
      name: '外链',
      items: [
        { title: 'Medium', url: 'https://medium.com/', description: '专业内容发布和阅读平台' },
        { title: 'Hashnode', url: 'https://www.promptzone.com/', description: 'AI提示词和技术资源社区' },
        { title: 'Write', url: 'https://write.as/', description: '简洁的写作和发布平台' },
        { title: 'Hacker News', url: 'https://news.ycombinator.com/', description: '正版HN 每天1-3条' },
        { title: '国产HN', url: 'https://news.catchideas.com/', description: '国产HN 不限 邀请码catchideas2025' }
      ]
    },
    {
      name: '找词',
      items: [
        { title: 'Trends', url: 'https://trends.google.com/trending?geo=US&hl=en-US', description: '谷歌趋势美国热门搜索' },
        { title: 'Chromewebstore', url: 'https://chromewebstore.google.com/', description: 'Chrome浏览器扩展商店' },
        { title: 'Google Play', url: 'https://play.google.com/', description: '安卓应用商店' },
        { title: 'Product Hunt', url: 'https://www.producthunt.com/', description: '新产品发现和推荐平台' },
        { title: '七麦数据', url: 'https://www.qimai.cn/', description: '移动应用数据分析平台' },
        { title: 'ASOTools', url: 'https://asotools.io/', description: '应用商店优化工具' },
        { title: '点点数据', url: 'https://app.diandian.com/', description: '小程序数据分析平台' },
        { title: 'Reddit', url: 'https://www.reddit.com/', description: '全球最大的社区讨论平台' },
        { title: 'Quora', url: 'https://www.quora.com/', description: '知识问答社区平台' },
        { title: '黑客新闻', url: 'https://news.ycombinator.com/', description: '技术创业资讯社区' },
        { title: 'Indie Hackers', url: 'https://www.indiehackers.com/', description: '独立开发者社区' },
        { title: 'AI产品收入排行榜', url: 'https://www.indiehackers.com/products?category=ai&revenueVerification=stripe&sorting=highest-revenue', description: 'Indie Hackers AI产品收入排行榜，Stripe验证的真实收入数据' },
        { title: 'Ambitious Founder', url: 'https://ambitiousfounder.com/', description: '创业者资源和故事分享' },
        { title: 'BetaList', url: 'https://betalist.com/', description: '新产品测试版发现平台' },
        { title: 'SaaSHub', url: 'https://www.saashub.com/', description: 'SaaS产品发现和替代方案平台，软件工具对比分析' },
        { title: 'PromptBase', url: 'https://promptbase.com/', description: 'AI提示词交易市场' },
        { title: 'Hugging Face', url: 'https://huggingface.co/', description: 'AI模型和数据集社区' },
        { title: 'GitHub', url: 'https://github.com/trending', description: '代码托管和开源项目平台' },
        { title: 'TikTok Hashtag', url: 'https://ads.tiktok.com/business/creativecenter/inspiration/popular/hashtag/pc/en', description: 'TikTok热门标签和创意灵感' },
        { title: 'YouTube Trending', url: 'https://www.youtube.com/feed/trending', description: 'YouTube热门视频趋势' },
        { title: 'X Explore', url: 'https://x.com/explore', description: 'X平台热门话题和趋势' },
        { title: 'Reddit Popular', url: 'https://www.reddit.com/r/popular/', description: 'Reddit热门内容和讨论' },
        { title: 'Vercel查词', url: 'https://www.google.com/search?q=site:vercel.app&tbs=qdr:d', description: 'Vercel部署站点搜索，支持时间筛选', isVercelSearch: true },
        { title: 'Netlify查词', url: 'https://www.google.com/search?q=site:netlify.app&tbs=qdr:d', description: 'Netlify部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'netlify.app' },
        { title: 'Render查词', url: 'https://www.google.com/search?q=site:onrender.com&tbs=qdr:d', description: 'Render部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'onrender.com' },
        { title: 'Surge查词', url: 'https://www.google.com/search?q=site:surge.sh&tbs=qdr:d', description: 'Surge部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'surge.sh' },
        { title: 'Firebase查词', url: 'https://www.google.com/search?q=site:web.app&tbs=qdr:d', description: 'Firebase部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'web.app' },
        { title: 'Firebase App查词', url: 'https://www.google.com/search?q=site:firebaseapp.com&tbs=qdr:d', description: 'Firebase App部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'firebaseapp.com' },
        { title: 'GitHub Pages查词', url: 'https://www.google.com/search?q=site:github.io&tbs=qdr:d', description: 'GitHub Pages部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'github.io' },
        { title: 'Glitch查词', url: 'https://www.google.com/search?q=site:glitch.me&tbs=qdr:d', description: 'Glitch部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'glitch.me' },
        { title: 'Railway查词', url: 'https://www.google.com/search?q=site:railway.app&tbs=qdr:d', description: 'Railway部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'railway.app' },
        { title: 'Cloudflare Pages查词', url: 'https://www.google.com/search?q=site:pages.dev&tbs=qdr:d', description: 'Cloudflare Pages部署站点搜索，支持时间筛选', isPlatformSearch: true, searchDomain: 'pages.dev' },
        { title: 'AI工具需求', url: 'https://theresanaiforthat.com/requests', description: 'AI工具需求收集平台，发现用户真实需求和痛点' },
        { title: 'Toolify AI请求', url: 'https://www.toolify.ai/ai-request', description: 'Toolify AI工具请求平台，用户提交AI工具需求和建议' },
        { title: '最新上架的AI', url: 'https://www.toolify.ai/zh/new', description: 'Toolify AI最新上架工具，发现最前沿的AI应用' },
        { title: 'AppSumo', url: 'https://appsumo.com/software/?sort=latest', description: '软件折扣平台，发现最新的SaaS工具和软件优惠' },
        { title: 'StackSocial', url: 'https://stacksocial.com', description: '技术产品折扣平台，软件和数字产品特价销售' },
        { title: 'DealMirror', url: 'https://dealmirror.com', description: '软件优惠聚合平台，汇集各类软件折扣信息' },
        { title: 'DigitalThink', url: 'https://digitalthink.io', description: '数字产品折扣平台，原Dealify，专注软件和工具优惠' },
        { title: 'Pinterest Trends', url: 'https://trends.pinterest.com/', description: 'Pinterest趋势和流行内容' },
        { title: 'Google Ads Keyword Planner', url: 'https://ads.google.com/home/<USER>/keyword-planner/', description: '谷歌广告关键词规划工具' },
        { title: '互站网', url: 'https://task.huzhan.com/', description: '网站交易和任务外包平台' }
      ]
    },
    {
      name: '技术栈',
      items: [
        { title: 'NextAuth', url: 'https://next-auth.js.org/', description: 'Next.js身份验证解决方案' },
        { title: 'Shadcn/ui', url: 'https://ui.shadcn.com/', description: '现代React UI组件库' },
        { title: '21st.dev', url: 'https://21st.dev/home', description: '现代化UI组件和设计资源' },
        { title: 'ReactBits', url: 'https://reactbits.dev/', description: 'React UI组件和代码片段' },
        { title: 'Next.js - DEV Community', url: 'https://dev.to/t/nextjs', description: 'Next.js开发者社区文章' },
        { title: 'Nextjs on Hashnode', url: 'https://hashnode.com/n/nextjs', description: 'Hashnode上的Next.js内容' },
        { title: 'Next.js reddit', url: 'https://www.reddit.com/r/nextjs/', description: 'Next.js Reddit讨论社区' },
        { title: 'Next.js Stack Overflow', url: 'https://stackoverflow.com/questions/tagged/next.js', description: 'Next.js技术问答' },
        { title: 'Next.js', url: 'https://nextjs.org/', description: 'React全栈开发框架官网' }
      ]
    }
  ]

  // 获取所有分类名称
  const categories = ['all', ...bookmarkCategories.map(cat => cat.name)]

  // 过滤书签
  const filteredBookmarks = bookmarkCategories.filter(category => {
    if (selectedCategory !== 'all' && category.name !== selectedCategory) {
      return false
    }
    
    if (searchTerm) {
      return category.items.some(item => 
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.url.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    
    return true
  }).map(category => ({
    ...category,
    items: category.items.filter(item =>
      !searchTerm || 
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.url.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }))



  return (
    <div className="min-h-screen bg-background">
        {/* 使用统一前台顶部组件 */}
        <FrontendHeader />



      {/* 搜索和筛选区域 */}
      <section className="py-6 px-4 bg-background border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="bg-card rounded-xl shadow-sm border p-6">
              {/* 搜索框 - 居中显示 */}
              <div className="flex justify-center mb-6">
                <div className="relative w-full max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="搜索书签..."
                    className="pl-10 h-12 text-lg border-input focus:border-primary focus:ring-primary"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* 分类筛选标签 - 在下方 */}
              <div className="flex flex-wrap justify-center gap-2">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className={selectedCategory === category
                      ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 border-0"
                      : "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 transition-all duration-200"
                    }
                  >
                    {category === 'all' ? '全部' : category}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* 书签内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          {filteredBookmarks.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Search className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">未找到相关书签</h3>
                <p className="text-muted-foreground">
                  试试调整搜索关键词或选择其他分类
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-8">
              {filteredBookmarks.map((category) => (
                <div key={category.name}>
                  <div className="flex items-center mb-6">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mr-3">
                      <Star className="h-5 w-5 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-foreground">{category.name}</h2>
                    <Badge className="ml-3 bg-gray-100 text-gray-700">
                      {category.items.length} 个
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {category.items.map((item, index) => {
                      if (item.isVercelSearch) {
                        return <VercelSearchCard key={index} item={item} />
                      }
                      if (item.isPlatformSearch) {
                        return <PlatformSearchCard key={index} item={item} />
                      }
                      return (
                        <Card key={index} className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-base font-semibold text-foreground group-hover:text-primary transition-colors flex items-center">
                              <Globe className="h-4 w-4 mr-2 text-muted-foreground group-hover:text-primary" />
                              <span className="truncate">{item.title}</span>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <p className="text-sm text-muted-foreground mb-3 h-10 overflow-hidden">
                              {item.description || '优质网站资源'}
                            </p>
                            <Button
                              asChild
                              size="sm"
                              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200"
                            >
                              <a
                                href={item.url}
                                target={category.name === '站内工具' ? '_blank' : (item.url.startsWith('/') ? '_self' : '_blank')}
                                {...(category.name === '站内工具' ? {} : (!item.url.startsWith('/') ? { rel: "nofollow noopener noreferrer" } : {}))}
                                className="flex items-center justify-center"
                              >
                                <ExternalLink className="h-4 w-4 mr-2" />
                                访问网站
                              </a>
                            </Button>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
